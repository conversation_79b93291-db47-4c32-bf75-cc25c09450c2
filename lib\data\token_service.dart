import 'package:shared_preferences/shared_preferences.dart';

class TokenService {
  static const String _tokenKey = 'huggingface_token';

  /// Get the stored Hugging Face token
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// Save the Hugging Face token
  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  /// Remove the stored token
  static Future<void> removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
  }

  /// Check if a token is stored
  static Future<bool> hasToken() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  /// Validate token format
  static bool isValidTokenFormat(String token) {
    return token.isNotEmpty && 
           token.startsWith('hf_') && 
           token.length >= 20;
  }
}
