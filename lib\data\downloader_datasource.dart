import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:offline_menu_translator/domain/download_model.dart';
import 'package:offline_menu_translator/data/token_service.dart';

class ModelDownloadService {
  static final _gemma = FlutterGemmaPlugin.instance;
  static ModelFileManager get _modelManager => _gemma.modelManager;

  /// Check if a model is already downloaded
  static Future<bool> isModelDownloaded(AvailableModel model) async {
    try {
      // Get the app documents directory
      final directory = await getApplicationDocumentsDirectory();
      final modelFile = File('${directory.path}/${model.modelFilename}');

      // Check if file exists and has reasonable size
      if (await modelFile.exists()) {
        final fileSize = await modelFile.length();
        // If file is larger than 10MB, consider it downloaded
        // This is a simple heuristic - you might want more sophisticated validation
        return fileSize > 10 * 1024 * 1024; // 10MB
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking model existence: $e');
      }
      return false;
    }
  }

  /// Download a model from network with progress tracking
  static Stream<double> downloadModelWithProgress(AvailableModel model) async* {
    try {
      if (kDebugMode) {
        print('Starting download for: ${model.name}');
        print('Model URL: ${model.modelUrl}');
        print('Model filename: ${model.modelFilename}');
      }

      final token = await TokenService.getToken();
      if (token == null || token.isEmpty) {
        throw Exception(
          'No access token found. Please configure your Hugging Face token.',
        );
      }

      if (kDebugMode) {
        print('Token found: ${token.substring(0, 10)}...');
      }

      // Get the app documents directory
      final directory = await getApplicationDocumentsDirectory();
      final modelFile = File('${directory.path}/${model.modelFilename}');

      // Create the request with authentication
      final request = http.Request('GET', Uri.parse(model.modelUrl));
      request.headers['Authorization'] = 'Bearer $token';
      request.headers['User-Agent'] = 'flutter_gemma_app/1.0';

      // Send the request
      if (kDebugMode) {
        print('Sending HTTP request to: ${model.modelUrl}');
      }

      final response = await request.send();

      if (kDebugMode) {
        print('HTTP response status: ${response.statusCode}');
        print('Response headers: ${response.headers}');
      }

      if (response.statusCode != 200) {
        throw Exception(
          'Failed to download model: HTTP ${response.statusCode}',
        );
      }

      final contentLength = response.contentLength ?? 0;
      if (contentLength == 0) {
        throw Exception('Unable to determine file size');
      }

      // Create the file and start downloading
      final sink = modelFile.openWrite();
      int downloadedBytes = 0;

      try {
        await for (final chunk in response.stream) {
          sink.add(chunk);
          downloadedBytes += chunk.length;

          // Yield progress as a value between 0.0 and 1.0
          final progress = downloadedBytes / contentLength;
          yield progress.clamp(0.0, 1.0);
        }
      } finally {
        await sink.close();
      }

      // Verify the download completed successfully
      if (downloadedBytes != contentLength) {
        await modelFile.delete();
        throw Exception(
          'Download incomplete: expected $contentLength bytes, got $downloadedBytes',
        );
      }

      if (kDebugMode) {
        print('Model downloaded successfully: ${model.modelFilename}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error downloading model: $e');
      }
      rethrow;
    }
  }

  /// Download a model from network (without progress tracking)
  static Future<void> downloadModel(AvailableModel model) async {
    // Use the progress version and ignore progress updates
    await for (final _ in downloadModelWithProgress(model)) {
      // Just consume the stream without doing anything with progress
    }
  }

  /// Set the model path for use with flutter_gemma
  static Future<void> setModelPath(String modelFilename) async {
    try {
      // Get the full path to the downloaded model
      final directory = await getApplicationDocumentsDirectory();
      final modelPath = '${directory.path}/$modelFilename';

      // Verify the file exists
      final modelFile = File(modelPath);
      if (!await modelFile.exists()) {
        throw Exception('Model file not found: $modelPath');
      }

      // Set the model path in flutter_gemma
      await _modelManager.setModelPath(modelPath);

      if (kDebugMode) {
        print('Model path set successfully: $modelPath');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error setting model path: $e');
      }
      rethrow;
    }
  }

  /// Delete a downloaded model
  static Future<void> deleteModel(String modelFilename) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final modelFile = File('${directory.path}/$modelFilename');

      if (await modelFile.exists()) {
        await modelFile.delete();
        if (kDebugMode) {
          print('Model deleted successfully: $modelFilename');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting model: $e');
      }
      rethrow;
    }
  }
}
