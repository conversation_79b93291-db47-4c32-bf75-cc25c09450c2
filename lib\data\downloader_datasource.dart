import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:offline_menu_translator/domain/download_model.dart';
import 'package:offline_menu_translator/data/token_service.dart';

class ModelDownloadService {
  static final _gemma = FlutterGemmaPlugin.instance;
  static ModelFileManager get _modelManager => _gemma.modelManager;

  /// Check if a model is already downloaded
  static Future<bool> isModelDownloaded(AvailableModel model) async {
    try {
      // Set the model path to check if it exists
      await _modelManager.setModelPath(model.modelFilename);

      // Try to get the file path - if it exists, the model is downloaded
      // This is a simple check - in practice you might want more sophisticated validation
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Model not found: $e');
      }
      return false;
    }
  }

  /// Download a model from network with progress tracking
  static Stream<double> downloadModelWithProgress(AvailableModel model) async* {
    try {
      final token = await TokenService.getToken();
      if (token == null || token.isEmpty) {
        throw Exception(
          'No access token found. Please configure your Hugging Face token.',
        );
      }

      // Set the model filename first
      await _modelManager.setModelPath(model.modelFilename);

      // Use the ModelFileManager to download from network with progress
      await for (final progress
          in _modelManager.downloadModelFromNetworkWithProgress(
            model.modelUrl,
          )) {
        yield progress / 100.0; // Convert percentage to 0-1 range
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error downloading model: $e');
      }
      rethrow;
    }
  }

  /// Download a model from network (without progress tracking)
  static Future<void> downloadModel(AvailableModel model) async {
    try {
      final token = await TokenService.getToken();
      if (token == null || token.isEmpty) {
        throw Exception(
          'No access token found. Please configure your Hugging Face token.',
        );
      }

      // Set the model filename first
      await _modelManager.setModelPath(model.modelFilename);

      await _modelManager.downloadModelFromNetwork(model.modelUrl);
    } catch (e) {
      if (kDebugMode) {
        print('Error downloading model: $e');
      }
      rethrow;
    }
  }

  /// Set the model path for use
  static Future<void> setModelPath(String modelFilename) async {
    await _modelManager.setModelPath(modelFilename);
  }

  /// Delete a downloaded model
  static Future<void> deleteModel() async {
    await _modelManager.deleteModel();
  }
}
