class LanguageSupport {
  static const Map<String, List<String>> fallbackHierarchy = {
    'vi': ['en', 'zh'], // Vietnamese falls back to English then Chinese
    'th': ['en', 'my'], // Thai falls back to English then Burmese
    'ar': ['en', 'fa'], // Arabic falls back to English then Persian
    'hi': ['en', 'bn'], // Hindi falls back to English then Bengali
    'fi': ['en', 'et'], // Finnish falls back to English then Estonian
  };

  static const Set<String> tonalLanguages = {
    'vi', // Vietnamese
    'th', // Thai
    'zh', // Chinese (Mandarin)
    'my', // Burmese
    'yue', // Cantonese
  };

  static const Set<String> complexScripts = {
    'ar', // Arabic
    'fa', // Persian
    'hi', // Hindi
    'bn', // Bengali
    'th', // Thai
    'dv', // Divehi
    'km', // Khmer
  };

  static String getPromptForLanguage(String langCode, String text) {
    String basePrompt = 'Translate the following text to English';

    if (tonalLanguages.contains(langCode)) {
      basePrompt +=
          '. Pay special attention to tonal markers and their meaning';
    }

    if (complexScripts.contains(langCode)) {
      basePrompt +=
          '. Ensure proper handling of the script and diacritical marks';
    }

    return '$basePrompt:\n$text';
  }

  static String getFallbackLanguage(String primaryLang) {
    return fallbackHierarchy[primaryLang]?.first ?? 'en';
  }
}
