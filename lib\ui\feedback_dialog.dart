import 'package:flutter/material.dart';

class FeedbackDialog extends StatefulWidget {
  const FeedbackDialog({super.key});

  @override
  State<FeedbackDialog> createState() => _FeedbackDialogState();
}

class _FeedbackDialogState extends State<FeedbackDialog> {
  final _feedbackController = TextEditingController();
  bool _isAccurate = true;
  final List<String> _selectedIssues = [];

  final List<String> _commonIssues = [
    'Wrong cultural context',
    'Incorrect tonal marks',
    'Missing diacritical marks',
    'Incorrect script rendering',
    'Wrong dish description',
    'Missing dietary information',
  ];

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return AlertDialog(
      icon: Icon(Icons.feedback_outlined, color: colorScheme.primary, size: 28),
      title: Text(
        'Translation Feedback',
        style: Theme.of(
          context,
        ).textTheme.headlineSmall?.copyWith(color: colorScheme.onSurface),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How was the translation quality?',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: colorScheme.onSurface),
            ),
            const SizedBox(height: 16),
            // Material 3 segmented button for accuracy
            SegmentedButton<bool>(
              segments: const [
                ButtonSegment<bool>(
                  value: true,
                  label: Text('Accurate'),
                  icon: Icon(Icons.check_circle_outline),
                ),
                ButtonSegment<bool>(
                  value: false,
                  label: Text('Needs Work'),
                  icon: Icon(Icons.error_outline),
                ),
              ],
              selected: {_isAccurate},
              onSelectionChanged: (Set<bool> newSelection) {
                setState(() {
                  _isAccurate = newSelection.first;
                });
              },
            ),
            if (!_isAccurate) ...[
              const SizedBox(height: 24),
              Text(
                'What could be improved?',
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(color: colorScheme.onSurface),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _commonIssues.map((issue) {
                  return FilterChip(
                    label: Text(issue),
                    selected: _selectedIssues.contains(issue),
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedIssues.add(issue);
                        } else {
                          _selectedIssues.remove(issue);
                        }
                      });
                    },
                    backgroundColor: colorScheme.surfaceContainerHighest,
                    selectedColor: colorScheme.secondaryContainer,
                    checkmarkColor: colorScheme.onSecondaryContainer,
                  );
                }).toList(),
              ),
            ],
            const SizedBox(height: 24),
            TextField(
              controller: _feedbackController,
              decoration: InputDecoration(
                labelText: 'Additional Comments (Optional)',
                hintText: 'Share any specific feedback...',
                prefixIcon: Icon(
                  Icons.comment_outlined,
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        FilledButton.icon(
          onPressed: () {
            final feedback = StringBuffer();
            feedback.writeln(
              'Quality: ${_isAccurate ? "Accurate" : "Needs Work"}',
            );

            if (_selectedIssues.isNotEmpty) {
              feedback.writeln('Issues: ${_selectedIssues.join(", ")}');
            }

            if (_feedbackController.text.isNotEmpty) {
              feedback.writeln('Comments: ${_feedbackController.text}');
            }

            Navigator.of(context).pop(feedback.toString());
          },
          icon: const Icon(Icons.send, size: 18),
          label: const Text('Submit Feedback'),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }
}
