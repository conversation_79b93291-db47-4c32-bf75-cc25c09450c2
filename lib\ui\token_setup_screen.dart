import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:offline_menu_translator/ui/animations.dart';

class TokenSetupScreen extends StatefulWidget {
  const TokenSetupScreen({super.key});

  @override
  State<TokenSetupScreen> createState() => _TokenSetupScreenState();
}

class _TokenSetupScreenState extends State<TokenSetupScreen> {
  final _tokenController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isValidating = false;
  bool _obscureToken = true;
  String? _validationError;

  @override
  void initState() {
    super.initState();
    _loadExistingToken();
  }

  @override
  void dispose() {
    _tokenController.dispose();
    super.dispose();
  }

  Future<void> _loadExistingToken() async {
    final prefs = await SharedPreferences.getInstance();
    final existingToken = prefs.getString('huggingface_token');
    if (existingToken != null && existingToken.isNotEmpty) {
      _tokenController.text = existingToken;
    }
  }

  Future<bool> _validateToken(String token) async {
    if (token.isEmpty) {
      setState(() {
        _validationError = 'Token cannot be empty';
      });
      return false;
    }

    if (!token.startsWith('hf_')) {
      setState(() {
        _validationError =
            'Invalid token format. Hugging Face tokens start with "hf_"';
      });
      return false;
    }

    if (token.length < 20) {
      setState(() {
        _validationError = 'Token appears to be too short';
      });
      return false;
    }

    // Basic validation passed
    setState(() {
      _validationError = null;
    });
    return true;
  }

  Future<void> _saveToken() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isValidating = true;
    });

    final token = _tokenController.text.trim();
    final isValid = await _validateToken(token);

    if (isValid) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('huggingface_token', token);

      if (mounted) {
        Navigator.of(context).pop(token);
      }
    }

    setState(() {
      _isValidating = false;
    });
  }

  Future<void> _openHuggingFaceTokenPage() async {
    const url = 'https://huggingface.co/settings/tokens';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Setup Access Token'),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: StaggeredListAnimation(
            duration: AppAnimations.medium3,
            staggerDelay: AppAnimations.short3,
            children: [
              // Header section
              FadeInAnimation(
                duration: AppAnimations.medium2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: colorScheme.primaryContainer.withValues(
                          alpha: 0.3,
                        ),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.security,
                            size: 32,
                            color: colorScheme.primary,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Hugging Face Access Token',
                                  style: Theme.of(context).textTheme.titleLarge
                                      ?.copyWith(fontWeight: FontWeight.w600),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Required to download AI models',
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(
                                        color: colorScheme.onSurfaceVariant,
                                      ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Instructions
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: colorScheme.primary),
                          const SizedBox(width: 8),
                          Text(
                            'How to get your token:',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildInstructionStep(
                        '1',
                        'Create a free account at huggingface.co',
                      ),
                      _buildInstructionStep(
                        '2',
                        'Go to Settings → Access Tokens',
                      ),
                      _buildInstructionStep(
                        '3',
                        'Create a new token with "Read" permissions',
                      ),
                      _buildInstructionStep(
                        '4',
                        'Copy and paste the token below',
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: _openHuggingFaceTokenPage,
                          icon: const Icon(Icons.open_in_new),
                          label: const Text('Open Hugging Face Tokens Page'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Token input
              TextFormField(
                controller: _tokenController,
                obscureText: _obscureToken,
                decoration: InputDecoration(
                  labelText: 'Access Token',
                  hintText: 'hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
                  prefixIcon: const Icon(Icons.key),
                  suffixIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(
                          _obscureToken
                              ? Icons.visibility
                              : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscureToken = !_obscureToken;
                          });
                        },
                      ),
                      IconButton(
                        icon: const Icon(Icons.paste),
                        onPressed: () async {
                          final clipboardData = await Clipboard.getData(
                            Clipboard.kTextPlain,
                          );
                          if (clipboardData?.text != null) {
                            _tokenController.text = clipboardData!.text!;
                          }
                        },
                      ),
                    ],
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  errorText: _validationError,
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your access token';
                  }
                  if (!value.startsWith('hf_')) {
                    return 'Invalid token format';
                  }
                  return null;
                },
                onChanged: (value) {
                  if (_validationError != null) {
                    setState(() {
                      _validationError = null;
                    });
                  }
                },
              ),
              const SizedBox(height: 24),

              // Privacy notice
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.privacy_tip_outlined,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Your token is stored securely on your device and only used to download models.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Save button
              SizedBox(
                width: double.infinity,
                child: FilledButton.icon(
                  onPressed: _isValidating ? null : _saveToken,
                  icon: _isValidating
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.save),
                  label: Text(_isValidating ? 'Validating...' : 'Save Token'),
                  style: FilledButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInstructionStep(String number, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(text, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }
}
