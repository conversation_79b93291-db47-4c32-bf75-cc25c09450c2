import '../domain/download_model.dart';

class ModelsRepository {
  static const List<AvailableModel> availableModels = [
    AvailableModel(
      id: 'gemma-3n-e2b',
      name: 'Gemma 3 Nano E2B',
      description: 'Lightweight multimodal model with vision support. Perfect for on-device inference with text and image capabilities.',
      modelUrl: 'https://huggingface.co/google/gemma-3n-E2B-it-litert-preview/resolve/main/gemma-3n-E2B-it-int4.task',
      modelFilename: 'gemma-3n-E2B-it-int4.task',
      type: ModelType.gemma3Nano2B,
      capability: ModelCapability.multimodal,
      size: '1.5B parameters (~800MB)',
      languages: ['English', 'Spanish', 'Japanese', 'Chinese', 'Vietnamese', 'Thai', 'Arabic', 'Hindi', 'Korean', 'Finnish'],
    ),
    AvailableModel(
      id: 'gemma-3n-e4b',
      name: 'Gemma 3 Nano E4B',
      description: 'Enhanced multimodal model with better performance. Supports vision and text with improved accuracy.',
      modelUrl: 'https://huggingface.co/google/gemma-3n-E4B-it-litert-preview/resolve/main/gemma-3n-E4B-it-int4.task',
      modelFilename: 'gemma-3n-E4B-it-int4.task',
      type: ModelType.gemma3Nano4B,
      capability: ModelCapability.multimodal,
      size: '1.5B parameters (~1.2GB)',
      languages: ['English', 'Spanish', 'Japanese', 'Chinese', 'Vietnamese', 'Thai', 'Arabic', 'Hindi', 'Korean', 'Finnish'],
    ),
    AvailableModel(
      id: 'gemma-3-1b',
      name: 'Gemma 3 1B',
      description: 'Compact text-only model optimized for fast inference. Great for text generation and conversation.',
      modelUrl: 'https://huggingface.co/litert-community/Gemma3-1B-IT/resolve/main/gemma3-1b-it-int4.task',
      modelFilename: 'gemma3-1b-it-int4.task',
      type: ModelType.gemma3_1B,
      capability: ModelCapability.textOnly,
      size: '1B parameters (~600MB)',
      languages: ['English', 'Spanish', 'Japanese', 'Chinese', 'Vietnamese', 'Thai', 'Arabic', 'Hindi', 'Korean', 'Finnish'],
    ),
    AvailableModel(
      id: 'gemma-2b',
      name: 'Gemma 2B',
      description: 'Balanced text model with good performance and reasonable size. Suitable for most text tasks.',
      modelUrl: 'https://huggingface.co/google/gemma-2b-it/resolve/main/model.safetensors',
      modelFilename: 'gemma-2b-it.safetensors',
      type: ModelType.gemma2B,
      capability: ModelCapability.textOnly,
      size: '2B parameters (~1.4GB)',
      languages: ['English', 'Spanish', 'Japanese', 'Chinese', 'Vietnamese', 'Thai', 'Arabic', 'Hindi', 'Korean', 'Finnish'],
    ),
    AvailableModel(
      id: 'phi-4',
      name: 'Phi-4 Mini',
      description: 'Microsoft\'s efficient small language model. Fast inference with good reasoning capabilities.',
      modelUrl: 'https://huggingface.co/litert-community/Phi-4-mini-instruct/resolve/main/phi-4-mini-instruct-int4.task',
      modelFilename: 'phi-4-mini-instruct-int4.task',
      type: ModelType.phi4,
      capability: ModelCapability.textOnly,
      size: '14B parameters (~8GB)',
      languages: ['English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Chinese', 'Japanese'],
    ),
    AvailableModel(
      id: 'deepseek',
      name: 'DeepSeek R1 Distill',
      description: 'Distilled version of DeepSeek R1. Optimized for reasoning and code generation tasks.',
      modelUrl: 'https://huggingface.co/litert-community/DeepSeek-R1-Distill-Qwen-1.5B/resolve/main/deepseek-r1-distill-qwen-1.5b-int4.task',
      modelFilename: 'deepseek-r1-distill-qwen-1.5b-int4.task',
      type: ModelType.deepSeek,
      capability: ModelCapability.textOnly,
      size: '1.5B parameters (~900MB)',
      languages: ['English', 'Chinese', 'Spanish', 'French', 'German', 'Japanese', 'Korean'],
    ),
  ];

  static List<AvailableModel> getModelsByCapability(ModelCapability capability) {
    return availableModels.where((model) => model.capability == capability).toList();
  }

  static List<AvailableModel> getMultimodalModels() {
    return getModelsByCapability(ModelCapability.multimodal);
  }

  static List<AvailableModel> getTextOnlyModels() {
    return getModelsByCapability(ModelCapability.textOnly);
  }

  static AvailableModel? getModelById(String id) {
    try {
      return availableModels.firstWhere((model) => model.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<AvailableModel> searchModels(String query) {
    final lowercaseQuery = query.toLowerCase();
    return availableModels.where((model) {
      return model.name.toLowerCase().contains(lowercaseQuery) ||
          model.description.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }
}
