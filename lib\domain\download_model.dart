enum ModelType {
  gemma2B,
  gemma7B,
  gemma2_2B,
  gemma3_1B,
  gemma3Nano2B,
  gemma3Nano4B,
  phi4,
  deepSeek,
}

enum ModelCapability { textOnly, multimodal }

class AvailableModel {
  final String id;
  final String name;
  final String description;
  final String modelUrl;
  final String modelFilename;
  final ModelType type;
  final ModelCapability capability;
  final String size;
  final List<String> languages;
  final bool requiresToken;

  const AvailableModel({
    required this.id,
    required this.name,
    required this.description,
    required this.modelUrl,
    required this.modelFilename,
    required this.type,
    required this.capability,
    required this.size,
    required this.languages,
    this.requiresToken = true,
  });
}

class DownloadModel {
  final String modelUrl;
  final String modelFilename;

  DownloadModel({required this.modelUrl, required this.modelFilename});
}
