import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:offline_menu_translator/domain/download_model.dart';

class InferenceService {
  static final _gemma = FlutterGemmaPlugin.instance;
  static InferenceModel? _currentModel;
  static Chat? _currentChat;
  static AvailableModel? _loadedModel;

  /// Get the current loaded model info
  static AvailableModel? get loadedModel => _loadedModel;

  /// Check if a model is currently loaded
  static bool get isModelLoaded => _currentModel != null;

  /// Check if chat is initialized
  static bool get isChatInitialized => _currentChat != null;

  /// Load and initialize a model for inference
  static Future<void> loadModel(AvailableModel model) async {
    try {
      // Close existing model if any
      await closeModel();

      // Set the model path
      await _gemma.modelManager.setModelPath(model.modelFilename);

      // Determine model type for flutter_gemma
      final modelType = _getFlutterGemmaModelType(model.type);

      // Create the inference model
      _currentModel = await _gemma.createModel(
        modelType: modelType,
        preferredBackend: PreferredBackend.gpu,
        maxTokens: model.capability == ModelCapability.multimodal ? 4096 : 2048,
        supportImage: model.capability == ModelCapability.multimodal,
        maxNumImages: model.capability == ModelCapability.multimodal ? 1 : 0,
      );

      _loadedModel = model;

      if (kDebugMode) {
        print('Model loaded successfully: ${model.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading model: $e');
      }
      rethrow;
    }
  }

  /// Initialize chat with the loaded model
  static Future<void> initializeChat({
    double temperature = 0.8,
    int randomSeed = 1,
    int topK = 1,
  }) async {
    if (_currentModel == null) {
      throw Exception('No model loaded. Please load a model first.');
    }

    try {
      // Close existing chat if any
      await closeChat();

      _currentChat = await _currentModel!.createChat(
        temperature: temperature,
        randomSeed: randomSeed,
        topK: topK,
        supportImage: _loadedModel?.capability == ModelCapability.multimodal,
      );

      if (kDebugMode) {
        print('Chat initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing chat: $e');
      }
      rethrow;
    }
  }

  /// Send a message to the chat and get streaming response
  static Stream<String> sendMessage(Message message) async* {
    if (_currentChat == null) {
      throw Exception('Chat not initialized. Please initialize chat first.');
    }

    try {
      await _currentChat!.addQueryChunk(message);
      
      await for (final token in _currentChat!.generateChatResponseAsync()) {
        yield token;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error sending message: $e');
      }
      rethrow;
    }
  }

  /// Send a message to the chat and get complete response
  static Future<String> sendMessageSync(Message message) async {
    if (_currentChat == null) {
      throw Exception('Chat not initialized. Please initialize chat first.');
    }

    try {
      await _currentChat!.addQueryChunk(message);
      return await _currentChat!.generateChatResponse();
    } catch (e) {
      if (kDebugMode) {
        print('Error sending message: $e');
      }
      rethrow;
    }
  }

  /// Create a session for single inference (without chat history)
  static Future<Session> createSession({
    double temperature = 0.8,
    int randomSeed = 1,
    int topK = 1,
  }) async {
    if (_currentModel == null) {
      throw Exception('No model loaded. Please load a model first.');
    }

    try {
      return await _currentModel!.createSession(
        temperature: temperature,
        randomSeed: randomSeed,
        topK: topK,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error creating session: $e');
      }
      rethrow;
    }
  }

  /// Close the current chat
  static Future<void> closeChat() async {
    if (_currentChat != null) {
      // Note: Chat doesn't have a close method in the current API
      _currentChat = null;
      if (kDebugMode) {
        print('Chat closed');
      }
    }
  }

  /// Close the current model and chat
  static Future<void> closeModel() async {
    await closeChat();
    
    if (_currentModel != null) {
      await _currentModel!.close();
      _currentModel = null;
      _loadedModel = null;
      if (kDebugMode) {
        print('Model closed');
      }
    }
  }

  /// Get the appropriate ModelType for flutter_gemma
  static ModelType _getFlutterGemmaModelType(ModelType modelType) {
    switch (modelType) {
      case ModelType.gemma2B:
      case ModelType.gemma7B:
      case ModelType.gemma2_2B:
      case ModelType.gemma3_1B:
      case ModelType.gemma3Nano2B:
      case ModelType.gemma3Nano4B:
        return ModelType.gemmaIt;
      case ModelType.phi4:
        return ModelType.phi;
      case ModelType.deepSeek:
        return ModelType.gemmaIt; // Use gemmaIt as fallback
    }
  }

  /// Check if the current model supports multimodal input
  static bool get supportsMultimodal {
    return _loadedModel?.capability == ModelCapability.multimodal;
  }

  /// Get model information
  static String? get modelName => _loadedModel?.name;
  static String? get modelSize => _loadedModel?.size;
  static List<String>? get supportedLanguages => _loadedModel?.languages;
}
