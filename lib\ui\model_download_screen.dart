import 'package:flutter/material.dart';
import 'package:offline_menu_translator/data/downloader_datasource.dart';
import 'package:offline_menu_translator/domain/download_model.dart';
import 'package:offline_menu_translator/ui/animations.dart';
import 'package:offline_menu_translator/ui/license_help_dialog.dart';

class ModelDownloadScreen extends StatefulWidget {
  final AvailableModel model;

  const ModelDownloadScreen({super.key, required this.model});

  @override
  State<ModelDownloadScreen> createState() => _ModelDownloadScreenState();
}

class _ModelDownloadScreenState extends State<ModelDownloadScreen> {
  double _downloadProgress = 0.0;
  bool _isDownloading = false;
  bool _isCompleted = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkIfModelExists();
  }

  Future<void> _checkIfModelExists() async {
    final exists = await ModelDownloadService.isModelDownloaded(widget.model);
    if (exists && mounted) {
      setState(() {
        _isCompleted = true;
        _downloadProgress = 1.0;
      });
    }
  }

  Future<void> _startDownload() async {
    setState(() {
      _isDownloading = true;
      _errorMessage = null;
      _downloadProgress = 0.0;
    });

    try {
      print('Starting download for model: ${widget.model.name}');
      print('Model URL: ${widget.model.modelUrl}');

      await for (final progress
          in ModelDownloadService.downloadModelWithProgress(widget.model)) {
        print('Download progress: ${(progress * 100).toStringAsFixed(1)}%');
        if (mounted) {
          setState(() {
            _downloadProgress = progress;
          });
        }
      }

      print('Download completed successfully');
      if (mounted) {
        setState(() {
          _isDownloading = false;
          _isCompleted = true;
          _downloadProgress = 1.0;
        });
      }
    } catch (e) {
      print('Download error: $e');
      if (mounted) {
        setState(() {
          _isDownloading = false;
          _errorMessage = e.toString();
        });

        // Show license help dialog if it's a 403 error
        if (e.toString().contains('Access denied') ||
            e.toString().contains('accept the model license')) {
          _showLicenseHelpDialog();
        }
      }
    }
  }

  void _onComplete() {
    Navigator.of(context).pop(widget.model);
  }

  void _showLicenseHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => LicenseHelpDialog(
        modelName: widget.model.name,
        modelUrl: widget.model.modelUrl,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Download Model'), centerTitle: false),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            Expanded(
              child: StaggeredListAnimation(
                duration: AppAnimations.medium3,
                staggerDelay: AppAnimations.short3,
                children: [
                  // Model info card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: colorScheme.primaryContainer,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  widget.model.capability ==
                                          ModelCapability.multimodal
                                      ? Icons.image
                                      : Icons.text_fields,
                                  color: colorScheme.onPrimaryContainer,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.model.name,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge
                                          ?.copyWith(
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      widget.model.size,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            color: colorScheme.onSurfaceVariant,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            widget.model.description,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: colorScheme.onSurfaceVariant),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Download progress section
                  if (_isDownloading ||
                      _isCompleted ||
                      _errorMessage != null) ...[
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                if (_isDownloading)
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: colorScheme.primary,
                                    ),
                                  )
                                else if (_isCompleted)
                                  Icon(
                                    Icons.check_circle,
                                    color: Colors.green,
                                    size: 20,
                                  )
                                else if (_errorMessage != null)
                                  Icon(
                                    Icons.error,
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                const SizedBox(width: 12),
                                Text(
                                  _isDownloading
                                      ? 'Downloading...'
                                      : _isCompleted
                                      ? 'Download Complete'
                                      : 'Download Failed',
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(fontWeight: FontWeight.w600),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            LinearProgressIndicator(
                              value: _downloadProgress,
                              backgroundColor:
                                  colorScheme.surfaceContainerHighest,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _errorMessage != null
                                    ? Colors.red
                                    : _isCompleted
                                    ? Colors.green
                                    : colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '${(_downloadProgress * 100).toStringAsFixed(1)}%',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                            ),
                            if (_errorMessage != null) ...[
                              const SizedBox(height: 12),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.red.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.error_outline,
                                      color: Colors.red,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        _errorMessage!,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall
                                            ?.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // Add help button for license errors
                              if (_errorMessage != null &&
                                  (_errorMessage!.contains('Access denied') ||
                                      _errorMessage!.contains(
                                        'accept the model license',
                                      ))) ...[
                                const SizedBox(height: 12),
                                SizedBox(
                                  width: double.infinity,
                                  child: OutlinedButton.icon(
                                    onPressed: _showLicenseHelpDialog,
                                    icon: const Icon(Icons.help_outline),
                                    label: const Text('How to Accept License'),
                                  ),
                                ),
                              ],
                            ],
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Info section
                  if (!_isDownloading && !_isCompleted)
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'This model will be downloaded to your device for offline use. Make sure you have a stable internet connection.',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),

            // Action buttons
            SafeArea(
              child: Column(
                children: [
                  if (!_isDownloading && !_isCompleted)
                    SizedBox(
                      width: double.infinity,
                      child: FilledButton.icon(
                        onPressed: _startDownload,
                        icon: const Icon(Icons.download),
                        label: const Text('Start Download'),
                        style: FilledButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                      ),
                    )
                  else if (_isCompleted)
                    SizedBox(
                      width: double.infinity,
                      child: FilledButton.icon(
                        onPressed: _onComplete,
                        icon: const Icon(Icons.check),
                        label: const Text('Continue'),
                        style: FilledButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                      ),
                    )
                  else if (_errorMessage != null)
                    SizedBox(
                      width: double.infinity,
                      child: FilledButton.icon(
                        onPressed: _startDownload,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Retry Download'),
                        style: FilledButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
