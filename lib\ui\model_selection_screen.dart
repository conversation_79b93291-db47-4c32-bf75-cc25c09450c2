import 'package:flutter/material.dart';
import 'package:offline_menu_translator/data/models_repository.dart';
import 'package:offline_menu_translator/domain/download_model.dart';
import 'package:offline_menu_translator/ui/animations.dart';

class ModelSelectionScreen extends StatefulWidget {
  const ModelSelectionScreen({super.key});

  @override
  State<ModelSelectionScreen> createState() => _ModelSelectionScreenState();
}

class _ModelSelectionScreenState extends State<ModelSelectionScreen> {
  String _searchQuery = '';
  ModelCapability? _selectedCapability;
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<AvailableModel> get _filteredModels {
    List<AvailableModel> models = ModelsRepository.availableModels;

    // Filter by capability if selected
    if (_selectedCapability != null) {
      models = models.where((model) => model.capability == _selectedCapability).toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      models = ModelsRepository.searchModels(_searchQuery);
    }

    return models;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Select AI Model'),
        centerTitle: false,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search and filter section
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              border: Border(
                bottom: BorderSide(color: colorScheme.outlineVariant),
              ),
            ),
            child: Column(
              children: [
                // Search bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search models...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 12),
                // Capability filter chips
                Row(
                  children: [
                    Text(
                      'Filter by capability:',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Wrap(
                        spacing: 8,
                        children: [
                          FilterChip(
                            label: const Text('All'),
                            selected: _selectedCapability == null,
                            onSelected: (selected) {
                              setState(() {
                                _selectedCapability = null;
                              });
                            },
                          ),
                          FilterChip(
                            label: const Text('Text Only'),
                            selected: _selectedCapability == ModelCapability.textOnly,
                            onSelected: (selected) {
                              setState(() {
                                _selectedCapability = selected ? ModelCapability.textOnly : null;
                              });
                            },
                          ),
                          FilterChip(
                            label: const Text('Multimodal'),
                            selected: _selectedCapability == ModelCapability.multimodal,
                            onSelected: (selected) {
                              setState(() {
                                _selectedCapability = selected ? ModelCapability.multimodal : null;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Models list
          Expanded(
            child: _filteredModels.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: _filteredModels.length,
                    itemBuilder: (context, index) {
                      final model = _filteredModels[index];
                      return FadeInAnimation(
                        duration: AppAnimations.medium2,
                        curve: AppAnimations.emphasized,
                        child: ModelCard(
                          model: model,
                          onTap: () => _selectModel(model),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No models found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filter criteria',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _selectModel(AvailableModel model) {
    Navigator.of(context).pop(model);
  }
}

class ModelCard extends StatelessWidget {
  final AvailableModel model;
  final VoidCallback onTap;

  const ModelCard({
    super.key,
    required this.model,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      model.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  _buildCapabilityChip(context),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                model.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.storage,
                    size: 16,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    model.size,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.language,
                    size: 16,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${model.languages.length} languages',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCapabilityChip(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isMultimodal = model.capability == ModelCapability.multimodal;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isMultimodal ? colorScheme.primaryContainer : colorScheme.secondaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isMultimodal ? Icons.image : Icons.text_fields,
            size: 14,
            color: isMultimodal ? colorScheme.onPrimaryContainer : colorScheme.onSecondaryContainer,
          ),
          const SizedBox(width: 4),
          Text(
            isMultimodal ? 'Multimodal' : 'Text Only',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isMultimodal ? colorScheme.onPrimaryContainer : colorScheme.onSecondaryContainer,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
